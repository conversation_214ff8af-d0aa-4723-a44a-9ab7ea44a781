# coding=utf-8
"""
Created on 2012-12-31

@author: ETHAN
"""
from django.db import models
from model_managers import project_model_manager
from gatesidelib.common.simplelogger import SimpleLogger
from django.contrib.auth.models import User


class ProjectModel(models.Model):
    CreationTime = models.DateTimeField(auto_now_add=True)
    IsActive = models.BooleanField(default=True)

    class Meta:
        abstract = True


class IssueConfigModel(models.Model):
    IsActive = models.BooleanField(default=True)

    class Meta:
        abstract = True


class Requirement(ProjectModel):
    """
        Status: 1:待评审 2:排期 3:开发中 4:已提测 5:测试中 6:待上线 7:已上线
    """
    ProjectID = models.IntegerField()
    Title = models.CharField(max_length=200)
    ReleaseDate = models.DateField(null=True)
    StartDate = models.DateTimeField(null=True)
    FinishedDate = models.DateTimeField(null=True)
    ExecutiveOwner = models.IntegerField(default=0, null=True)
    Owner = models.IntegerField(default=0)
    Creator = models.IntegerField()
    Progress = models.FloatField(default=0)
    Description = models.TextField(null=True, blank=True)
    Status = models.IntegerField(default=1)
    Priority = models.IntegerField(default=0, null=True, blank=True)
    RiskLevel = models.IntegerField(default=1)
    TestPointFile = models.IntegerField(default=0)
    FortestingID = models.IntegerField(default=0)
    Version = models.IntegerField(default=0)
    Module = models.IntegerField(default=0, null=True, blank=True)
    DevelopTime = models.DateTimeField(null=True)
    TestTime = models.DateTimeField(null=True)
    WaitOnlineTime = models.DateTimeField(null=True)
    objects = project_model_manager.RequirementManager()

    class Meta:
        verbose_name = '需求表'
        app_label = "project"
        db_table = "project_requirement"


class RequirementTaskMap(ProjectModel):
    RequirementID = models.IntegerField()
    TaskID = models.IntegerField()
    TaskType = models.IntegerField(default=1, help_text="1 任务,2 提测")
    objects = project_model_manager.RequirementTaskManager()

    class Meta:
        app_label = "project"
        db_table = "project_requirement_task"


class FortestingTaskMap(ProjectModel):
    FortestingID = models.IntegerField()
    TaskID = models.IntegerField()
    TaskType = models.IntegerField(default=1, help_text="1 任务,2 提测")
    objects = project_model_manager.FortestingTaskManager()

    class Meta:
        app_label = "project"
        db_table = "project_fortesting_task"


class Task(ProjectModel):
    ProjectID = models.IntegerField()
    Title = models.CharField(max_length=200)
    DeadLine = models.DateTimeField(null=True)
    StartDate = models.DateTimeField(null=True)
    FinishedDate = models.DateTimeField(null=True)
    WorkHours = models.IntegerField()
    Owner = models.IntegerField(default=0)
    OrderID = models.IntegerField(null=True)
    Creator = models.IntegerField()
    Progress = models.FloatField(default=0)
    Description = models.CharField(max_length=1000, null=True, blank=True)
    Tags = models.CharField(max_length=50, null=True)
    Status = models.IntegerField()
    Parent = models.IntegerField(null=True, default=None)
    Priority = models.IntegerField(default=1)
    TaskType = models.IntegerField(default=1,
                                   help_text="1 服务端任务,2 测试任务,3 前端任务 4 UI/UE任务,5 客户端,6 部署, 7 测试评审")
    Version = models.IntegerField(default=0)
    objects = project_model_manager.TaskManager()

    class Meta:
        app_label = "project"
        db_table = "project_task"


class ProjectTaskOwner(ProjectModel):
    Owner = models.IntegerField()
    Unit = models.FloatField(default=0)
    Task = models.IntegerField()
    TaskType = models.IntegerField(default=1)
    Version = models.IntegerField(default=0)
    objects = project_model_manager.TaskOwnerManager()

    class Meta:
        app_label = "project"
        db_table = "project_task_owner"


class ProjectTaskType(ProjectModel):
    '''
        1 服务端任务,2 测试任务,3 前端任务 4 UI/UE任务,5 客户端,6 部署, 7 测试评审
    '''
    TypeName = models.CharField(max_length=10)
    Label = models.CharField(max_length=20)
    LabelColor = models.CharField(max_length=20)
    objects = project_model_manager.TaskTypeManager()

    class Meta:
        app_label = "project"
        db_table = "project_task_type"


class ProjectTaskStatus(ProjectModel):
    """
        task: 0 待处理，1 处理中，2 已完成 3 已暂停
        fortesting 1 待提测 2 已提测 3 测试中 4 测试完成 5 已上线 6 废弃 7 打回
        requirement status: 1 待评审，2 待排期 3 开发中，4 已提测 5 测试中 6 待上线 7 已上线，8 暂停
        req priority  p0, p1 p2 p3 p4
        req risk: 无，低， 中，高
    """
    Status = models.IntegerField()
    Desc = models.CharField(max_length=20)
    Type = models.IntegerField(default=1, help_text="1 Task, 2 Fortesting, 3 需求状态，4 需求优先级，5 需求风险, 6 用例优先级，7 用例执行结果")
    objects = project_model_manager.TaskStatusManager()

    class Meta:
        app_label = "project"
        db_table = "project_task_status"


class TagOwner(ProjectModel):
    '''
        OwnerType: 1 通用,2 ci任务分类,3 agent,4 提测,5 需求
    '''
    Owner = models.IntegerField()
    OwnerType = models.IntegerField()
    TagID = models.IntegerField()
    objects = project_model_manager.TagOwnerManager()

    class Meta:
        app_label = "project"
        db_table = "tag_owner"


class ProjectTaskDependency(ProjectModel):
    Predecessor = models.IntegerField()
    Successor = models.IntegerField()
    Type = models.IntegerField()
    Version = models.IntegerField(default=0)
    objects = project_model_manager.TaskDependencyManager()

    class Meta:
        app_label = "project"
        db_table = "project_task_dependency"


class Version(ProjectModel):
    VProjectID = models.IntegerField()
    VVersion = models.CharField(max_length=50)
    VStartDate = models.DateField(null=True)
    VReleaseDate = models.DateField(null=True)
    VReleaseDate = models.DateField(null=True)
    VDescription = models.CharField(max_length=1000)
    VersionFiled = models.BooleanField(default=False)
    objects = project_model_manager.VersionManager()

    class Meta:
        app_label = "project"
        db_table = "project_version"


class TestApplication(ProjectModel):
    """
        提测
        Status：1:待提测 2:已提测 3:测试中 4:测试完成 5:已上线 6：废弃
        RiskLevel: 0:P0
    """
    ProjectID = models.IntegerField()
    VersionID = models.IntegerField(null=True, blank=True)
    Topic = models.CharField(null=True, max_length=200)
    Commitor = models.IntegerField(default=0)
    TestingFeature = models.TextField(blank=True)
    TestingAdvice = models.TextField(blank=True)
    Attachment = models.CharField(null=True, max_length=50)
    Status = models.IntegerField(verbose_name='状态')
    EmailNotificationStatus = models.CharField(max_length=20, default="0,0,0,0,0")
    Creator = models.IntegerField(default=0, verbose_name='创建人')
    Testers = models.IntegerField(verbose_name='测试人')
    CommitTime = models.DateTimeField(null=True)
    ExpectCommitDate = models.DateTimeField(null=True, blank=True)
    TestingDeadLineDate = models.DateTimeField(null=True, blank=True)
    TestingStartDate = models.DateTimeField(null=True, blank=True)
    TestingFinishedDate = models.DateTimeField(null=True, blank=True)
    TestingReleaseDate = models.DateTimeField(null=True, blank=True)
    SelfValidateResult = models.IntegerField(null=True, blank=True, default=0)
    ProjectModuleID = models.IntegerField(null=True, blank=True, default=0)
    BVTPassed = models.BooleanField(null=True, blank=True, default=False)
    ProjectCode = models.IntegerField(null=True, blank=True, default=0)
    Description = models.TextField(null=True, blank=True, verbose_name='描述')
    Link = models.TextField(null=True, blank=True, verbose_name='链接')
    priority = models.IntegerField(null=True, blank=True, default=0, verbose_name='优先级')
    RiskLevel = models.IntegerField(null=True, blank=True, default=0, verbose_name='风险级别')
    objects = project_model_manager.TestApplicationManager()

    class Meta:
        app_label = "project"
        db_table = "project_test_application"


class ProjectXmindTopic(ProjectModel):
    MindFileID = models.IntegerField()
    Text = models.CharField(max_length=500, null=True)
    OriginalID = models.CharField(max_length=15)
    Note = models.CharField(max_length=500, null=True)
    Priority = models.IntegerField(null=True)
    Progress = models.IntegerField(null=True)
    Image = models.CharField(max_length=1000, null=True)
    Link = models.CharField(max_length=1000, null=True)
    BackgroundColor = models.CharField(max_length=50, null=True)
    FontColor = models.CharField(max_length=50, null=True)
    FontSize = models.IntegerField(default=14)
    FontFamily = models.CharField(max_length=100, null=True)
    Parent = models.IntegerField()
    Project = models.IntegerField()
    UpdateTime = models.DateTimeField(auto_now=True)
    IsLeaf = models.BooleanField(default=False)
    objects = project_model_manager.MindTopicManager()

    class Meta:
        app_label = "project"
        db_table = "project_xmind_topic"


class ProjectXmindTopicTagMap(ProjectModel):
    TopicID = models.IntegerField()
    OriginalID = models.CharField(max_length=15)
    TagID = models.IntegerField()
    objects = project_model_manager.TopicTagMapManager()

    class Meta:
        app_label = "project"
        db_table = "topic_tag_map"


class ProjectTestCase(ProjectModel):
    Title = models.CharField(max_length=500, null=True)
    Desc = models.CharField(max_length=1000, null=True, blank=True)
    ExpectResult = models.CharField(max_length=1000, null=True, blank=True)
    Precondition = models.CharField(max_length=1000, null=True, blank=True)
    Priority = models.IntegerField(null=True)
    Parent = models.IntegerField()
    Project = models.IntegerField()
    Module = models.IntegerField(null=True)
    RunTimes = models.IntegerField(null=True)
    UpdateTime = models.DateTimeField(auto_now=True)
    IsGroup = models.BooleanField(default=False)
    Scenes = models.IntegerField(null=True)
    automate = models.IntegerField(null=True)
    accessTest = models.IntegerField(null=True)
    automatedCompletion = models.IntegerField(null=True)
    Creator = models.IntegerField(null=True)
    Status = models.IntegerField(null=True, default=0, help_text="1:废弃")
    objects = project_model_manager.TestCaseManager()

    class Meta:
        app_label = "project"
        db_table = "project_test_case"


class ProjectTestCaseAttachment(ProjectModel):
    CaseID = models.IntegerField()
    FileID = models.IntegerField()
    CaseType = models.IntegerField(default=0, help_text="1 测试用例，2 测试计划")
    objects = project_model_manager.TestCaseAttachmentManager()

    class Meta:
        app_label = "project"
        db_table = "test_case_attachment"


class ProjectTestCaseTags(ProjectModel):
    CaseID = models.IntegerField()
    Tag = models.IntegerField()
    objects = project_model_manager.TestCaseTagManager()

    class Meta:
        app_label = "project"
        db_table = "test_case_tag"


class ProjectTestCaseIssue(ProjectModel):
    CaseID = models.IntegerField()
    IssueID = models.IntegerField()
    CaseType = models.IntegerField(default=0, help_text="1 测试用例，2 测试计划")
    objects = project_model_manager.TestCaseIssueManager()

    class Meta:
        app_label = "project"
        db_table = "test_case_issue"


class ProjectTestPlan(ProjectModel):
    Title = models.CharField(max_length=200, null=True)
    Desc = models.CharField(max_length=500, null=True, blank=True)
    Version = models.IntegerField()
    Project = models.IntegerField()
    Creator = models.IntegerField()
    Owner = models.IntegerField()
    Status = models.IntegerField(default=1, help_text="1 新建，2 测试中，3 已完成，4 已归档, 5 暂停")
    IncludeAllCase = models.BooleanField(default=False)
    CaseCount = models.IntegerField(default=0)
    pass_rate = models.DecimalField(max_digits=9, decimal_places=2, null=True, blank=True)
    requireNum = models.IntegerField(default=1)
    objects = project_model_manager.TestPlanManager()
    history = models.CharField(max_length=1000, null=True)
    StartTime = models.DateTimeField(default=None)
    FinishTime = models.DateTimeField(default=None)
    EstimatedStartTime = models.DateField(null=True, blank=True)
    EstimatedFinishTime = models.DateField(null=True, blank=True)

    class Meta:
        app_label = "project"
        db_table = "project_test_plan"


class ProjectTestPlanCase(ProjectModel):
    TestPlan = models.ForeignKey(default=0, to="ProjectTestPlan", related_name="plan_case", db_constraint=False,
                                 on_delete=models.DO_NOTHING, db_column="TestPlan", verbose_name="测试计划")
    TestCase = models.ForeignKey(default=0, to="ProjectTestCase", related_name="plancase_testcase", db_constraint=False,
                                 on_delete=models.DO_NOTHING, db_column="TestCase", verbose_name="测试用例")
    IsGroup = models.BooleanField(default=False)
    IsChecked = models.BooleanField(default=False)
    TestResult = models.IntegerField(help_text='0 未开始 1通过，2 受阻碍 3 重测，4 失败')
    Owner = models.IntegerField()
    Creator = models.IntegerField()
    Parent = models.IntegerField()
    # Parent = models.ForeignKey(to="ProjectTestCase", null=True, blank=True, related_name="children", db_constraint=False,
    #                            on_delete=models.DO_NOTHING, db_column="Parent", verbose_name="父级")
    objects = project_model_manager.TestPlanCaseManager()

    @property
    def test_case(self):
        return self.TestCase

    class Meta:
        app_label = "project"
        db_table = "project_test_plan_case"


class ProjectTestCaseExecResult(ProjectModel):
    case_id = models.ForeignKey(to="ProjectTestPlanCase", related_name="exec_result", db_constraint=False,
                                on_delete=models.CASCADE, db_column="case_id", verbose_name="TestPlanCase测试结果")
    Owner = models.IntegerField()
    TestResult = models.IntegerField(null=True, help_text='0未开始, 1通过，2受阻碍 3重测，4失败')
    ActualResult = models.CharField(max_length=2048, null=True, verbose_name='实际结果')
    Comment = models.CharField(max_length=2048, null=True, verbose_name='创建人')
    objects = project_model_manager.TestCaseExecResultManager()

    class Meta:
        app_label = "project"
        db_table = "project_test_case_exec_result"


class ProjectTestPlanForTesting(ProjectModel):
    TestPlan = models.IntegerField()
    Fortesting = models.IntegerField()
    objects = project_model_manager.TestPlanFortestingManager()

    class Meta:
        app_label = "project"
        db_table = "project_test_plan_fortesting"


class ProjectXmindFile(ProjectModel):
    FileName = models.CharField(max_length=50, default='新建文件')
    FileType = models.IntegerField(help_text="1:测试点，2 冒烟执行记录 3 测试执行记录")
    ProjectID = models.IntegerField()
    VersionID = models.IntegerField()
    RootTopic = models.IntegerField()
    Theme = models.CharField(max_length=50, default='fresh-blue')
    Template = models.CharField(max_length=50, default='default')
    TestPointCount = models.IntegerField(default=0)
    Owner = models.IntegerField()
    objects = project_model_manager.MindFileManager()

    class Meta:
        app_label = "project"
        db_table = "project_xmind_file"


class WebHook(ProjectModel):
    WHProjectID = models.IntegerField()
    WHURL = models.CharField(max_length=500)
    WHParameters = models.CharField(max_length=500, null=True)
    WHLabel = models.CharField(max_length=50, null=True)
    WHIsDefault = models.BooleanField(default=False)
    WHCatagory = models.IntegerField()
    WHCreator = models.IntegerField()
    objects = project_model_manager.WebHookManager()

    class Meta:
        app_label = "project"
        db_table = "project_webhook"


class ProjectMember(ProjectModel):
    PMProjectID = models.IntegerField()
    PMRoleID = models.IntegerField()
    PMRoleType = models.IntegerField()
    PMMember = models.IntegerField()
    objects = project_model_manager.MemberManager()

    class Meta:
        app_label = "project"
        db_table = "project_member"


class Project(ProjectModel):
    PBTitle = models.CharField(max_length=100)
    PBKey = models.CharField(max_length=10)
    PBDescription = models.CharField(max_length=255, null=True, blank=True)
    PBVisiableLevel = models.IntegerField()
    PBPlatform = models.IntegerField()
    PBHttpUrl = models.CharField(max_length=255, null=True, blank=True)
    PBLead = models.IntegerField()
    PBAvatar = models.CharField(max_length=255, null=True)
    ProductSpace = models.IntegerField()
    PBCreator = models.IntegerField()
    objects = project_model_manager.ProjectManager()

    class Meta:
        app_label = "project"
        db_table = "project"


class ProductSpace(ProjectModel):
    Title = models.CharField(max_length=100)
    Key = models.CharField(max_length=10, null=True, blank=True)
    Creator = models.IntegerField(default=0)
    SpaceAdmin = models.IntegerField(default=0)
    LabelColor = models.CharField(max_length=25, null=True)
    objects = project_model_manager.ProductSpaceManager()

    class Meta:
        app_label = "project"
        db_table = "product_space"


class ProductSpaceUser(ProjectModel):
    ProductSpace = models.IntegerField()
    User = models.IntegerField()
    objects = project_model_manager.ProductSpaceUserManager()

    class Meta:
        app_label = "project"
        db_table = "product_space_user"


class ProjectModule(ProjectModel):
    Name = models.CharField(max_length=100)
    ProjectID = models.IntegerField()
    Description = models.CharField(max_length=255, null=True)
    objects = project_model_manager.ModuleManager()

    class Meta:
        app_label = "project"
        db_table = "project_module"


class Tag(ProjectModel):
    '''
    TagType: 1 通用,2 CI,3 Agent,4 提测，5 需求， 6 测试用例
    '''
    TagName = models.CharField(max_length=20)
    TagProjectID = models.IntegerField()
    TagColor = models.CharField(max_length=50, null=True)
    TagAvatar = models.CharField(max_length=255, null=True)
    TagType = models.IntegerField(default=1, help_text="1 通用，2 CI任务分类，3 Agent,4 提测，5 需求 6 测试用例")
    TagVisableLevel = models.IntegerField()
    TagOwner = models.IntegerField()
    TagValue = models.IntegerField()
    objects = project_model_manager.TagManager()

    class Meta:
        app_label = "project"
        db_table = "project_tag"


class ProjectRole(ProjectModel):
    PRName = models.CharField(max_length=20)
    PRColor = models.CharField(max_length=50, null=True)
    PRAuthGroup = models.IntegerField()
    PRRoleDesc = models.CharField(max_length=500, null=True)
    objects = project_model_manager.RoleManager()

    class Meta:
        app_label = "project"
        db_table = "project_role"


class ProjectArchive(ProjectModel):
    VersionID = models.IntegerField()
    ProjectID = models.CharField(max_length=50, null=True)
    HistoryID = models.IntegerField()
    Archive = models.IntegerField()
    objects = project_model_manager.ArchiveManager()

    class Meta:
        app_label = "project"
        db_table = "project_archive"


class ProjectDocument(ProjectModel):
    '''
    Type: 1 在线文件，2 上传文件 3 文件夹
    ReadOnly: 系统固定文件夹或文件,对用户只读
    '''
    ProjectID = models.IntegerField()
    Type = models.IntegerField()
    FileID = models.IntegerField()
    Owner = models.IntegerField()
    LockBy = models.IntegerField(null=True)
    ReadOnly = models.BooleanField(default=False)
    Parent = models.IntegerField(null=True)
    objects = project_model_manager.ProjectDocumentManager()

    class Meta:
        app_label = "project"
        db_table = "project_document"


class ProjectIssue(ProjectModel):
    issue_desc_default = "​<br/>​步骤：<br/>​实际结果：<br/>​期望结果：<br/>​备注：<br/>"
    Project = models.IntegerField()
    Version = models.IntegerField(verbose_name="版本号")
    Status = models.ForeignKey(default=2, to="ProjectIssueStatus", related_name="status_issue", db_constraint=False,
                               on_delete=models.DO_NOTHING, db_column="Status", verbose_name="问题状态")

    @property  # @property字段默认就是read_only，且不允许修改
    def status_name(self):
        return self.Status.Name

    @property
    def status_label(self):
        return self.Status.Label

    @property
    def status_label_style(self):
        return self.Status.LabelStyle

    Processor = models.IntegerField(verbose_name="问题经办人")
    Solver = models.IntegerField(verbose_name="问题解决者")
    Creator = models.IntegerField(verbose_name="问题创建人")

    Severity = models.ForeignKey(default=1, to="ProjectIssueSeverity", related_name="+", db_constraint=False,
                                 on_delete=models.DO_NOTHING, db_column="Severity", verbose_name="问题严重性")

    @property
    def severity_name(self):
        return self.Severity.Name

    @property
    def severity_label(self):
        return self.Severity.Label

    @property
    def severity_label_style(self):
        return self.Severity.LabelStyle

    Solution = models.ForeignKey(default=1, to="ProjectIssueResolvedResult", related_name="+", db_constraint=False,
                                 on_delete=models.DO_NOTHING, db_column="Solution", verbose_name="问题解决结果/解决方案")

    @property
    def solution_name(self):
        return self.Solution.Name

    @property
    def solution_label(self):
        return self.Solution.Label

    @property
    def solution_label_style(self):
        return self.Solution.LabelStyle

    Title = models.CharField(max_length=500, verbose_name="问题标题")
    Desc = models.TextField(null=True, default=issue_desc_default, verbose_name="问题描述")
    Module = models.IntegerField(verbose_name="问题模块")
    ProjectPhase = models.IntegerField(verbose_name="项目阶段")
    IssueCategory = models.ForeignKey(default=1, to="ProjectIssueCategory", related_name="+", db_constraint=False,
                                      on_delete=models.DO_NOTHING, db_column="IssueCategory", verbose_name="问题类别")

    @property
    def category_name(self):
        return self.IssueCategory.Name

    DeviceOS = models.IntegerField(default=0, verbose_name="设备系统")
    OSVersion = models.IntegerField(default=0, verbose_name="设备系统版本")
    Attachments = models.CharField(max_length=500, null=True, verbose_name="附件")
    ResolvedTime = models.DateTimeField(null=True, verbose_name="解决结果")
    ClosedTime = models.DateTimeField(null=True, verbose_name="关闭时间")
    ReopenCounts = models.IntegerField(default=0, verbose_name="重新打开次数")
    UpdateTime = models.DateTimeField(null=True, auto_now=True, verbose_name="更新时间")
    Priority = models.ForeignKey(default=1, to="ProjectIssuePriority", related_name="+", db_constraint=False,
                                 on_delete=models.DO_NOTHING, db_column="Priority", verbose_name="问题优先级")

    @property
    def priority_name(self):
        return self.Priority.Name

    Team = models.IntegerField(default=0)
    Requirement = models.IntegerField(default=0, verbose_name="需求ID")
    discover_way = models.IntegerField(default=0, verbose_name="发现方式", help_text='0 1手工 2自动化 3流量回放 4告警 5其他 6混沌工程')
    objects = project_model_manager.IssueManager()

    class Meta:
        app_label = "project"
        db_table = "project_issue"

    def get_field_verbose_name(self, field_name):
        result = ""
        try:
            if field_name:
                result = ProjectIssue._meta.get_field(field_name).verbose_name
        except Exception as ex:
            SimpleLogger.exception(ex)
        return result

    def get_field_name(self, field_name):
        return ProjectIssue._meta.get_field(field_name).name


class ProjectIssueDailyStatistics(ProjectModel):
    ProjectID = models.IntegerField()
    StatisticsDate = models.DateField()
    OpenedTotal = models.IntegerField()
    ClosedTotal = models.IntegerField()
    FixedTotal = models.IntegerField()
    OpenedToday = models.IntegerField()
    FixedToday = models.IntegerField()
    ReopenedToday = models.IntegerField()
    VersionID = models.IntegerField()
    objects = project_model_manager.IssueDailyStatisticsManager()

    class Meta:
        app_label = "project"
        db_table = "issue_daily_statistics"


class ProjectIssueVersionStatistics(ProjectModel):
    ProjectID = models.IntegerField()
    VersionID = models.IntegerField()
    StatisticsDate = models.DateField()
    IssueTotal = models.IntegerField()
    DimensionValue = models.IntegerField()
    Dimension = models.IntegerField()
    VersionID = models.IntegerField()
    objects = project_model_manager.IssueVersionStatisticsManager()

    class Meta:
        app_label = "project"
        db_table = "issue_version_statistics"

    class DimensionType:
        Severity = 1
        Category = 2
        ResolvedType = 3
        Module = 4


class IssueActivity(ProjectModel):
    Issue = models.IntegerField()
    OldValue = models.CharField(max_length=2500, null=True, blank=True)
    NewValue = models.CharField(max_length=2500, null=True, blank=True)
    FieldName = models.CharField(max_length=20, null=True, blank=True)
    FieldDesc = models.CharField(max_length=50, null=True)
    ActionType = models.IntegerField()
    ActionFlag = models.IntegerField()
    Creator = models.IntegerField()
    Message = models.CharField(max_length=2500, null=True)
    objects = project_model_manager.IssueActivityManager()

    class Meta:
        app_label = "project"
        db_table = "issue_activity"


class IssueFilter(ProjectModel):
    Project = models.IntegerField()
    Creator = models.IntegerField()
    Scope = models.IntegerField(default=1)
    FilterName = models.CharField(max_length=50)
    FilterString = models.CharField(max_length=500, null=True)
    FilterUIConfig = models.CharField(max_length=500, null=True)
    FilterCacheString = models.CharField(max_length=500, null=True)
    objects = project_model_manager.IssueFilterManager()

    class Meta:
        app_label = "project"
        db_table = "issue_filter"


class ProjectIssueStatus(IssueConfigModel):
    Value = models.IntegerField(primary_key=True)
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    Project = models.IntegerField()
    LabelStyle = models.CharField(max_length=50, null=True)
    Label = models.CharField(max_length=50, null=True)
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_issue_status"


class ProjectIssueResolvedResult(IssueConfigModel):
    Value = models.IntegerField(primary_key=True)
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    Project = models.IntegerField()
    LabelStyle = models.CharField(max_length=50, null=True)
    Label = models.CharField(max_length=50, null=True)
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_issue_resolved_result"


class ProjectIssueSeverity(IssueConfigModel):
    Value = models.IntegerField(primary_key=True)
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    Project = models.IntegerField()
    LabelStyle = models.CharField(max_length=50, null=True)
    Label = models.CharField(max_length=50, null=True)
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_issue_severity"


class ProjectIssuePriority(IssueConfigModel):
    Value = models.IntegerField(primary_key=True)
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_issue_priority"


class ProjectIssueCategory(IssueConfigModel):
    Value = models.IntegerField(primary_key=True)
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    Project = models.IntegerField()
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_issue_category"


class ProjectPhase(IssueConfigModel):
    Value = models.IntegerField()
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_phase"


class ProjectOS(IssueConfigModel):
    Value = models.IntegerField()
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    objects = project_model_manager.IssueConfigFieldManager()

    class Meta:
        app_label = "project"
        db_table = "project_os"


class ProjectOSVersion(IssueConfigModel):
    Value = models.IntegerField()
    Name = models.CharField(max_length=50, null=True)
    Desc = models.CharField(max_length=100, null=True)
    OS = models.IntegerField()
    objects = project_model_manager.ProjectOSVersionManager()

    class Meta:
        app_label = "project"
        db_table = "project_os_version"


class ProjectTestApplicationTimeStatistics(ProjectModel):
    '''
       data model for project statistics
    '''

    ProjectID = models.IntegerField()
    VersionID = models.IntegerField(default=0)
    TestApplicationID = models.IntegerField(default=0)
    TestDuration = models.IntegerField(default=0)
    StatisticsDate = models.DateField()
    StatisticsMonth = models.CharField(max_length=50, null=True)
    objects = project_model_manager.ProjectTestApplicationTimeStatisticsManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_application_time_statistics'


class ProjectTestApplicationNumberStatistics(ProjectModel):
    '''
       data model for project statistics
    '''

    ProjectID = models.IntegerField()
    VersionID = models.IntegerField(default=0)
    TestApplicationNumber = models.IntegerField(default=0)
    StatisticsDate = models.DateField()
    StatisticsMonth = models.CharField(max_length=50, null=True)
    objects = project_model_manager.ProjectTestApplicationNumberStatisticsManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_application_number_statistics'


class ProjectTestReport(ProjectModel):
    """
       data model for Project Test Report
    """
    Title = models.CharField(max_length=200)
    Project = models.IntegerField()
    ReportType = models.IntegerField(default=1, help_text="1 进度报告，2 冒烟测试，3 完成报告")
    Creator = models.IntegerField(default=0)
    Version = models.IntegerField(default=0, verbose_name="版本")
    Status = models.IntegerField(default=0, help_text="0: 正在生成，1 生成完毕 2 galaxy 绑定")
    Risk = models.IntegerField(default=0, help_text="1:无风险，2 中，3高")
    Progress = models.IntegerField(default=0, help_text="0: 正在生成，1 生成完毕")
    Summary = models.TextField(null=True)
    CaseStatistics = models.TextField(null=True)
    IssueStatistics = models.TextField(null=True)
    CopyID = models.IntegerField(default=0)
    Performance = models.TextField(null=True, blank=False, verbose_name="性能测试报告")
    Attachments = models.CharField(max_length=500, null=True, verbose_name="附件")
    objects = project_model_manager.ProjectTestReportManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_report'


class ProjectTestReportTestPlan(ProjectModel):
    '''
       data model for project statistics
    '''

    TestPlan = models.IntegerField()
    Report = models.IntegerField(default=0)
    objects = project_model_manager.ProjectTestReportTestPlanManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_report_test_plan'


class ProjectTestReportRequirements(ProjectModel):
    '''
       data model for project statistics
    '''

    TestPlan = models.IntegerField()
    Report = models.IntegerField(default=0)
    Status = models.IntegerField(default=0, help_text='6测试中，7:完成待上线')
    BVTResult = models.CharField(max_length=50, help_text='例如:0,0,0,0')
    Requirement = models.IntegerField(default=0)
    Title = models.CharField(max_length=200)
    objects = project_model_manager.ProjectTestReportRequirementManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_report_requirement'


class ProjectTestReportWebPartData(ProjectModel):
    '''
       data model for project statistics
    '''
    Title = models.CharField(max_length=200)
    WebPart = models.IntegerField(default=0)
    Report = models.IntegerField()
    WebPartData = models.TextField()
    Comments = models.CharField(max_length=50)
    objects = project_model_manager.ProjectTestReportWebPartDataManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_report_webpart_data'


class ProjectTestReportWebPart(ProjectModel):
    '''
       data model for project statistics
    '''
    Title = models.CharField(max_length=200)
    Desc = models.CharField(max_length=500, null=True)
    Project = models.IntegerField(default=0)
    objects = project_model_manager.ProjectTestReportWebPartManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_report_webpart'


class ProjectScenesInfo(ProjectModel):
    '''
       data model for project statistics
    '''

    scenes_id = models.IntegerField()
    scenes_name = models.CharField(max_length=255, null=True)
    project = models.IntegerField()
    objects = project_model_manager.ProjectScenesManager()

    class Meta:
        app_label = 'project'
        db_table = 'project_scense'


class ProjectTestCaseRepeatRate(ProjectModel):
    """
       test case repeat rate
    """
    project = models.IntegerField()
    repeat_rate = models.IntegerField()

    class Meta:
        app_label = 'project'
        db_table = 'project_test_case_repeat_rate'


class ProjectCaseReview(ProjectModel):
    Title = models.CharField(max_length=200, null=True)
    Desc = models.CharField(max_length=500, null=True, blank=True)
    Project = models.IntegerField()
    Creator = models.IntegerField()
    Status = models.IntegerField(default=1, help_text="1 进行中，2 已完成, 3 已归档")
    Deadline = models.DateTimeField(null=True)
    CaseCount = models.IntegerField(default=0)
    CaseReviewResult = models.TextField(null=True, blank=True, verbose_name='评审结论')
    objects = project_model_manager.ProjectCaseReviewManager()

    class Meta:
        app_label = "project"
        db_table = "project_case_review"


class ProjectCaseReviewTestcase(ProjectModel):
    CaseReview = models.IntegerField()
    TestCase = models.IntegerField()
    Creator = models.IntegerField()
    IsGroup = models.BooleanField(default=False)
    IsChecked = models.BooleanField(default=False)
    objects = project_model_manager.ProjectCaseReviewTestcaseManager()

    class Meta:
        app_label = "project"
        db_table = "project_case_review_testcase"


class ProjectCaseReviewReviewer(ProjectModel):
    CaseReview = models.IntegerField()
    Reviewer = models.IntegerField()

    class Meta:
        app_label = "project"
        db_table = "project_case_review_reviewer"


class ProjectCaseReviewRequirement(ProjectModel):
    case_review_id = models.IntegerField()
    requirement_id = models.IntegerField()

    class Meta:
        app_label = "project"
        db_table = "project_case_review_requirement"

# class ModelChangeLog(models.Model):
#     model_name = models.CharField(max_length=100)
#     field_name = models.CharField(max_length=100, null=True, blank=True)
#     old_value = models.TextField(null=True, blank=True)
#     new_value = models.TextField(null=True, blank=True)
#     changed_by = models.ForeignKey(User, on_delete=models.CASCADE)
#     changed_at = models.DateTimeField(auto_now_add=True)
#
#     def __str__(self):
#         return f"{self.model_name} - {self.field_name} changed by {self.changed_by}"
